package com.fulfillmen.plugin.woocommerce;

import java.util.List;
import pl.wtx.woocommerce.WooCommerceApiClient;
import pl.wtx.woocommerce.api.client.OrdersApi;
import pl.wtx.woocommerce.api.client.ProductShippingClassesApi;
import pl.wtx.woocommerce.api.client.ProductsApi;
import pl.wtx.woocommerce.api.client.invoker.ApiException;
import pl.wtx.woocommerce.api.client.model.MetaData;
import pl.wtx.woocommerce.api.client.model.Order;

/**
 * <AUTHOR>
 * @date 2025/8/29 14:10
 * @description: todo
 * @since 1.0.0
 */
public class WooCommerceApiClientUsageDemo {

  // 14423
  // ck_6f333356468ab0c2ad9056696d4b18f184edfdc9
  // cs_077b6323cf7131cb906a8bf638b615a793709fec
  // TODO: Set your WooCommerce API base path!
  private static final String API_BASE_PATH = "https://sundyshop.com/wp-json/wc/v3";
  private static final String API_USERNAME = "ck_6f333356468ab0c2ad9056696d4b18f184edfdc9";
  private static final String API_PASSWORD = "cs_077b6323cf7131cb906a8bf638b615a793709fec";
//

//  14333
//  private static final String API_BASE_PATH = "https://modu-design.com/wp-json/wc/v3";
//  private static final String API_USERNAME = "ck_29f89f06c7bdaf1c165e41a9e8c8c5368548276c";
//  private static final String API_PASSWORD = "cs_e611bdaf6c17c4903c21ceb826d135515eeff9c3";

  public static void main(String[] args) {

    System.out.println(">>> Start running the WooCommerceApiClientUsageDemo...");

    // Use WooCommerceApiClient(true) if you need to log API communication messages.
    WooCommerceApiClient apiClient = new WooCommerceApiClient();

    apiClient.setBasePath(API_BASE_PATH);
    apiClient.setUsername(API_USERNAME);
    apiClient.setPassword(API_PASSWORD);

    ProductsApi productsApi = new ProductsApi(apiClient);
    OrdersApi ordersApi = new OrdersApi(apiClient);
    ProductShippingClassesApi productShippingClassesApi = new ProductShippingClassesApi(apiClient);

    try {
//      List<ProductShippingClass> productShippingClasses = productShippingClassesApi
//          .listAllShippingClasses(null, null, null, null, null, null, null, null, null, null, null, null);
//      productShippingClasses.forEach(System.out::println);
//      List<Product> products = productsApi.listAllProducts(null, 1, 2, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
//          null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);

//      List<Order> orderList = ordersApi.listAllOrders(null, null, null, null, null, null, null, null,
//          null, null, null, 10, null, null, null, null, null, null, null, null);
//
//      orderList.forEach(Order::toJson);

      // Example list of customer's emails:
//      products.forEach(System.out::println);
//      Integer orderId = 4600;
      Integer orderId = 1659;
      Order order = ordersApi.retrieveOrderById(orderId);
      System.out.println(order.toJson());

      List<MetaData> metaDatas = order.getMetaData();
      metaDatas.forEach(meta ->
          System.out.println(meta.getKey() + " : " + meta.getValue()));
      MetaData trackingInfo = new MetaData();
//      MetaData trackingInfo = trackingInfo.id().key("_tracking_number").value();
      trackingInfo.setKey("_tracking_number");
      trackingInfo.setValue("HHWFR5245075101YQ");
      metaDatas.add(trackingInfo);

      MetaData trackingProvider = new MetaData();
      trackingInfo.setKey("_tracking_provider");
      trackingInfo.setValue("Fulfillmen");
      metaDatas.add(trackingProvider);
      order.setMetaData(metaDatas);
//      Order updateOrder = new Order();
//      updateOrder.setId(orderId);
//      updateOrder.setMetaData(metaDatas);
      ordersApi.updateOrderById(orderId, order);

    } catch (ApiException e) {
      System.err.println("Error occurred during API call: " + e);
    }

    System.out.println("<<< The WooCommerceApiClientUsageDemo has been finished.");

  }

}
