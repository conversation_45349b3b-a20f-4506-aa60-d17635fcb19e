<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.fulfillmen.support</groupId>
  <artifactId>woocommerce-client-example</artifactId>
  <version>1.0-SNAPSHOT</version>

  <properties>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>pl.wtx.woocommerce</groupId>
      <artifactId>woocommerce-api-client</artifactId>
      <version>0.9.7</version>
    </dependency>

    <!--<dependency>
      <groupId>uk.co.twinn.api</groupId>
      <artifactId>woocommerce-api-client</artifactId>
      <version>1.0.1</version>
    </dependency>-->

  </dependencies>

</project>